import sys
import os
import json
from datetime import datetime
import threading
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLineEdit, QLabel, QScrollArea, QFrame,
    QFileDialog, QMessageBox, QStatusBar, QComboBox,
    QGridLayout, QGraphicsDropShadowEffect, QListWidget, QListWidgetItem,
    QProgressDialog
)
from PySide6.QtGui import QFont, QCursor, QColor, QKeySequence, QAction, QShortcut
from PySide6.QtCore import Qt, Signal, QUrl, QThread, QMutex
from PySide6.QtGui import QDesktopServices

# --- Application and UI Configuration ---
APP_NAME = "Excel Grid Hub"
WIDTH = 1200  # Increased default width for better experience
HEIGHT = 900  # Increased default height
MIN_WIDTH = 600  # Minimum window width
MIN_HEIGHT = 500  # Minimum window height
STATE_FILE = "app_state.json"

# --- Responsive Grid Layout Configuration ---
# Dynamic card sizing based on screen width
CARD_MIN_WIDTH = 280    # Minimum card width
CARD_MAX_WIDTH = 380    # Maximum card width
CARD_HEIGHT = 100       # Card height
GRID_SPACING = 12       # Reduced spacing for more content
MARGIN_SMALL = 8        # Margins for small screens
MARGIN_MEDIUM = 12      # Margins for medium screens  
MARGIN_LARGE = 16       # Margins for large screens

# --- Responsive Breakpoints ---
BREAKPOINT_SMALL = 768   # Mobile/small tablet
BREAKPOINT_MEDIUM = 1024 # Medium screens
BREAKPOINT_LARGE = 1440  # Large screens

# --- Enhanced Modern Color System with Theme Management ---
class ThemeManager:
    """Centralized theme management for consistent styling."""

    LIGHT_THEME = {
        'primary': '#667eea',
        'primary_dark': '#5a67d8',
        'secondary': '#764ba2',
        'accent': '#f093fb',
        'success': '#48bb78',
        'warning': '#ed8936',
        'error': '#f56565',
        'background': '#f7fafc',
        'surface': '#ffffff',
        'text_primary': '#2d3748',
        'text_secondary': '#718096',
        'border': '#e2e8f0',
        'shadow': 'rgba(0, 0, 0, 0.1)'
    }

    DARK_THEME = {
        'primary': '#90cdf4',
        'primary_dark': '#63b3ed',
        'secondary': '#b794f6',
        'accent': '#f687b3',
        'success': '#68d391',
        'warning': '#fbb6ce',
        'error': '#fc8181',
        'background': '#1a202c',
        'surface': '#2d3748',
        'text_primary': '#f7fafc',
        'text_secondary': '#a0aec0',
        'border': '#4a5568',
        'shadow': 'rgba(0, 0, 0, 0.3)'
    }

    def __init__(self):
        self.current_theme = 'light'
        self.theme_colors = self.LIGHT_THEME.copy()

    def switch_theme(self, theme_name='auto'):
        """Switch between light/dark themes."""
        if theme_name == 'dark':
            self.current_theme = 'dark'
            self.theme_colors = self.DARK_THEME.copy()
        elif theme_name == 'light':
            self.current_theme = 'light'
            self.theme_colors = self.LIGHT_THEME.copy()
        # Auto theme detection could be added here

    def get_color(self, color_key):
        """Get color value from current theme."""
        return self.theme_colors.get(color_key, '#000000')

    def get_gradient_style(self, start_color, end_color):
        """Generate CSS gradient style."""
        return f"qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {start_color}, stop:1 {end_color})"

# Global theme manager instance
theme_manager = ThemeManager()

# Backward compatibility
THEME_COLORS = theme_manager.theme_colors

# Modern gradient combinations with improved accessibility
COLOR_GRADIENTS = [
    ["#667eea", "#764ba2"],  # Primary gradient - Purple to blue
    ["#f093fb", "#f5576c"],  # Pink to coral
    ["#4facfe", "#00f2fe"],  # Blue to cyan
    ["#43e97b", "#38f9d7"],  # Green to mint
    ["#fa709a", "#fee140"],  # Pink to yellow
    ["#a8edea", "#fed6e3"],  # Mint to pink
    ["#ff9a9e", "#fecfef"],  # Coral to lavender
    ["#ffecd2", "#fcb69f"],  # Cream to peach
    ["#a18cd1", "#fbc2eb"],  # Purple to pink
    ["#fad0c4", "#ffd1ff"],  # Peach to light pink
    ["#ff8a80", "#ffb74d"],  # Light red to orange
    ["#81c784", "#aed581"]   # Light green gradient
]

# Dark mode support (future enhancement)
DARK_THEME_COLORS = {
    'primary': '#90cdf4',
    'background': '#1a202c',
    'surface': '#2d3748',
    'text_primary': '#f7fafc',
    'text_secondary': '#a0aec0'
}

# --- Enhanced Excel File Detection with Performance Optimizations ---
EXCEL_EXTENSIONS = {
    '.xlsx', '.xls', '.xlsm', '.xlsb'
}

EXCEL_MIME_TYPES = {
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12'
}

# Cache for file validation results to avoid repeated checks
_file_validation_cache = {}
_cache_max_size = 1000

def is_excel_file(file_path):
    """Enhanced Excel file detection with caching and performance optimizations."""
    try:
        path = Path(file_path)

        # Quick extension check first (fastest)
        if path.suffix.lower() not in EXCEL_EXTENSIONS:
            return False

        # Check cache first
        cache_key = f"{file_path}:{path.stat().st_mtime}"
        if cache_key in _file_validation_cache:
            return _file_validation_cache[cache_key]

        # Check if file exists and is readable
        if not path.exists() or not path.is_file():
            result = False
        else:
            # Check file size (skip very large files that might cause issues)
            try:
                file_size = path.stat().st_size
                if file_size > 500 * 1024 * 1024:  # 500MB limit
                    result = False
                elif file_size == 0:  # Skip empty files
                    result = False
                else:
                    # For performance, trust extension for most cases
                    # Only do magic byte check for suspicious files
                    if path.suffix.lower() in {'.xlsx', '.xlsm', '.xlsb'}:
                        # Quick magic byte validation for modern Excel files
                        try:
                            with open(file_path, 'rb') as f:
                                header = f.read(4)
                                result = header[:2] == b'PK' or header == b'\xd0\xcf\x11\xe0'
                        except:
                            result = True  # Assume valid if can't read header
                    else:
                        result = True  # Trust .xls extension
            except:
                result = False

        # Cache the result (with size limit)
        if len(_file_validation_cache) >= _cache_max_size:
            # Remove oldest entries
            oldest_keys = list(_file_validation_cache.keys())[:100]
            for key in oldest_keys:
                del _file_validation_cache[key]

        _file_validation_cache[cache_key] = result
        return result

    except Exception:
        return False

class FileScanWorker(QThread):
    """Optimized worker thread for scanning files in background with batch processing."""
    file_found = Signal(str)  # Emit file path when found
    files_batch_found = Signal(list)  # Emit batch of files for better performance
    progress_update = Signal(int, str)  # Emit progress and current path
    finished = Signal(int)  # Emit total files found
    error = Signal(str)  # Emit error message

    def __init__(self, folder_path, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.should_stop = False
        self.mutex = QMutex()
        self.batch_size = 20  # Process files in batches for better performance
        self.skip_dirs = {
            '__pycache__', 'node_modules', '.git', '.svn', '.hg',
            'venv', 'env', '.vscode', '.idea', 'build', 'dist',
            'temp', 'tmp', 'cache', '.cache'
        }

    def run(self):
        """Optimized scan folder for Excel files with batch processing."""
        try:
            files_found = 0
            total_processed = 0
            found_files_batch = []

            # Use pathlib for better performance and filtering
            folder = Path(self.folder_path)

            # Pre-filter files during walk for better performance
            excel_files = []
            for root, dirs, files in os.walk(self.folder_path):
                # Skip hidden and system directories (modify dirs in-place)
                dirs[:] = [d for d in dirs if not d.startswith('.') and d.lower() not in self.skip_dirs]

                # Pre-filter files by extension for performance
                for file in files:
                    if (not file.startswith('.') and
                        any(file.lower().endswith(ext) for ext in EXCEL_EXTENSIONS)):
                        excel_files.append(os.path.join(root, file))

            total_files = len(excel_files)

            for i, file_path in enumerate(excel_files):
                # Check if we should stop
                self.mutex.lock()
                should_stop = self.should_stop
                self.mutex.unlock()

                if should_stop:
                    break

                total_processed += 1

                # Update progress less frequently for better performance
                if i % 10 == 0 or i == total_files - 1:
                    progress = int((i + 1) * 100 / total_files) if total_files > 0 else 0
                    self.progress_update.emit(progress, os.path.basename(file_path))

                # Validate Excel file (now with caching)
                if is_excel_file(file_path):
                    files_found += 1
                    found_files_batch.append(file_path)

                    # Emit individual file for immediate UI update
                    self.file_found.emit(file_path)

                    # Emit batch when full for efficient processing
                    if len(found_files_batch) >= self.batch_size:
                        self.files_batch_found.emit(found_files_batch.copy())
                        found_files_batch.clear()

                # Reduced sleep frequency for better performance
                if i % 100 == 0:  # Every 100 files instead of 50
                    self.msleep(1)

            # Emit remaining files in batch
            if found_files_batch:
                self.files_batch_found.emit(found_files_batch)

            self.finished.emit(files_found)

        except Exception as e:
            self.error.emit(str(e))

    def stop(self):
        """Stop the scanning process."""
        self.mutex.lock()
        self.should_stop = True
        self.mutex.unlock()

# --- Enhanced Modern UI Stylesheet ---
STYLESHEET = """
/* Base Application Styling */
QWidget {
    background-color: #f7fafc;
    color: #2d3748;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 10pt;
    font-weight: 400;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f7fafc, stop:1 #edf2f7);
}

/* Enhanced Clickable Labels */
QLabel#ClickableLabel {
    color: #667eea;
    font-weight: 500;
    padding: 2px 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

QLabel#ClickableLabel:hover {
    background-color: rgba(102, 126, 234, 0.1);
    text-decoration: underline;
    color: #5a67d8;
}

/* Modern Button Styling */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f7fafc);
    border: 1px solid #e2e8f0;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    color: #4a5568;
    min-height: 20px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f7fafc, stop:1 #edf2f7);
    border: 1px solid #cbd5e0;
    transform: translateY(-1px);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #edf2f7, stop:1 #e2e8f0);
    transform: translateY(0px);
}

QPushButton:focus {
    border: 2px solid #667eea;
    outline: none;
}

/* Enhanced Search Box */
QLineEdit {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 11pt;
    color: #2d3748;
    selection-background-color: #667eea;
}

QLineEdit:focus {
    border: 2px solid #667eea;
    background-color: #f8f9ff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

QLineEdit::placeholder {
    color: #a0aec0;
    font-style: italic;
}

/* Modern ComboBox */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 500;
    color: #4a5568;
    min-width: 120px;
}

QComboBox:hover {
    border: 1px solid #cbd5e0;
}

QComboBox:focus {
    border: 2px solid #667eea;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #718096;
    width: 0;
    height: 0;
}

/* Enhanced Scroll Areas */
QScrollArea {
    border: none;
    border-radius: 12px;
    background-color: transparent;
}

QScrollBar:vertical {
    border: none;
    background: rgba(0, 0, 0, 0.05);
    width: 12px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #cbd5e0, stop:1 #a0aec0);
    min-height: 20px;
    border-radius: 5px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #a0aec0, stop:1 #718096);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* Status Bar */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f7fafc);
    border-top: 1px solid #e2e8f0;
    padding: 8px;
    color: #718096;
    font-weight: 500;
}

/* Progress Dialog */
QProgressDialog {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
}

QProgressBar {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    color: #2d3748;
    background-color: #f7fafc;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, stop:1 #764ba2);
    border-radius: 6px;
    margin: 1px;
}
"""

class ClickableLabel(QLabel):
    clicked = Signal()
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.setObjectName("ClickableLabel")
    def mousePressEvent(self, event):
        self.clicked.emit()

class FileCard(QFrame):
    """Enhanced modern file card with improved interactions and visual design."""
    open_file_requested = Signal(dict)
    open_folder_requested = Signal(dict)
    favorite_toggled = Signal(dict, bool)

    def __init__(self, file_data, gradient_colors, card_width=None, parent=None):
        super().__init__(parent)
        self.file_data = file_data
        self.gradient_colors = gradient_colors
        self.is_hovered = False
        self.is_favorited = False
        self.card_width = card_width or CARD_MIN_WIDTH

        # Enhanced properties
        self.animation_duration = 200
        self.hover_scale = 1.02

        self.setFixedSize(self.card_width, CARD_HEIGHT)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.init_ui()
        self.apply_styles()
    
    def update_card_width(self, new_width):
        """Update card width for responsive design."""
        self.card_width = new_width
        self.setFixedSize(self.card_width, CARD_HEIGHT)
        # Update favorite button position if needed
        if hasattr(self, 'favorite_btn'):
            # The button is positioned using layout, so no manual positioning needed
            pass

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(18, 14, 18, 14)
        main_layout.setSpacing(8)

        # Header with enhanced Excel icon and file name
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)

        # Enhanced Excel icon with file type detection
        file_ext = os.path.splitext(self.file_data["name"])[1].lower()
        icon_text = "📊" if file_ext in ['.xlsx', '.xlsm'] else "📋"

        icon_label = QLabel(icon_text)
        icon_label.setStyleSheet("""
            font-size: 22px;
            color: white;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 4px 6px;
        """)
        icon_label.setFixedSize(36, 30)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)

        # File name with enhanced typography
        name_label = QLabel(self.file_data["name"])
        name_label.setFont(QFont("Segoe UI", 11, QFont.Weight.DemiBold))
        name_label.setStyleSheet("""
            color: white;
            font-weight: 600;
            background: transparent;
            line-height: 1.3;
        """)
        name_label.setWordWrap(True)
        name_label.setToolTip(f"📄 {self.file_data['name']}\n📁 {self.file_data['path']}")
        header_layout.addWidget(name_label, 1)

        # Favorite button (top right)
        self.favorite_btn = QPushButton("☆")
        self.favorite_btn.setFixedSize(24, 24)
        self.favorite_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.4);
            }
        """)
        self.favorite_btn.clicked.connect(self._toggle_favorite)
        header_layout.addWidget(self.favorite_btn)

        main_layout.addLayout(header_layout)

        # Enhanced folder path with better styling and hover effects
        path_layout = QHBoxLayout()
        path_layout.setSpacing(8)

        folder_icon = QLabel("📁")
        folder_icon.setStyleSheet("""
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            padding: 2px;
        """)
        path_layout.addWidget(folder_icon)

        # Truncate long paths intelligently
        folder_path = os.path.dirname(self.file_data["path"])
        display_path = self._truncate_path(folder_path, 35)

        path_label = ClickableLabel(display_path)
        path_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.85);
            font-size: 9pt;
            background: transparent;
            padding: 2px 4px;
            border-radius: 4px;
            border: 1px solid transparent;
        """)
        path_label.setToolTip(f"📁 Click to open folder:\n{folder_path}")
        path_label.clicked.connect(lambda: self.open_folder_requested.emit(self.file_data))

        # Add hover effect to path label
        def on_path_hover():
            path_label.setStyleSheet("""
                color: rgba(255, 255, 255, 1.0);
                font-size: 9pt;
                background: rgba(255, 255, 255, 0.1);
                padding: 2px 4px;
                border-radius: 4px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            """)

        def on_path_leave():
            path_label.setStyleSheet("""
                color: rgba(255, 255, 255, 0.85);
                font-size: 9pt;
                background: transparent;
                padding: 2px 4px;
                border-radius: 4px;
                border: 1px solid transparent;
            """)

        path_label.enterEvent = lambda e: on_path_hover()
        path_label.leaveEvent = lambda e: on_path_leave()

        path_layout.addWidget(path_label, 1)
        main_layout.addLayout(path_layout)

        # File metadata row
        metadata_layout = QHBoxLayout()
        metadata_layout.setSpacing(12)

        # File size
        size_label = QLabel(f"📊 {self._get_file_size()}")
        size_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 8pt;
            font-weight: 500;
        """)
        metadata_layout.addWidget(size_label)

        metadata_layout.addStretch()

        # Last modified
        date_label = QLabel(f"🕒 {self._get_file_date()}")
        date_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 8pt;
            font-weight: 500;
        """)
        metadata_layout.addWidget(date_label)

        main_layout.addLayout(metadata_layout)
        main_layout.addStretch()

    def _truncate_path(self, path, max_length):
        """Intelligently truncate file paths."""
        if len(path) <= max_length:
            return path

        # Try to keep the most relevant parts
        parts = path.split(os.sep)
        if len(parts) > 2:
            return f"...{os.sep}{parts[-2]}{os.sep}{parts[-1]}"
        else:
            return f"...{path[-max_length:]}"

    def _get_file_size(self):
        """Get human readable file size."""
        try:
            size_bytes = os.path.getsize(self.file_data["path"])
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024**2:
                return f"{size_bytes/1024:.1f} KB"
            elif size_bytes < 1024**3:
                return f"{size_bytes/(1024**2):.1f} MB"
            else:
                return f"{size_bytes/(1024**3):.1f} GB"
        except:
            return "Unknown"

    def _get_file_date(self):
        """Get human readable file modification date."""
        try:
            timestamp = os.path.getmtime(self.file_data["path"])
            return datetime.fromtimestamp(timestamp).strftime("%m/%d")
        except:
            return "Unknown"

    def _toggle_favorite(self):
        """Toggle favorite status."""
        self.is_favorited = not self.is_favorited
        self.favorite_btn.setText("★" if self.is_favorited else "☆")
        self.favorite_toggled.emit(self.file_data, self.is_favorited)

    def apply_styles(self):
        # Enhanced modern gradient background with better depth
        gradient_style = f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.gradient_colors[0]},
                    stop:1 {self.gradient_colors[1]});
                border-radius: 18px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.gradient_colors[0]},
                    stop:0.4 {self.gradient_colors[1]},
                    stop:1 {self.gradient_colors[0]});
                border: 1px solid rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }}
        """
        self.setStyleSheet(gradient_style)

        # Enhanced shadow effect with better depth
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(6)
        shadow.setColor(QColor(0, 0, 0, 40))
        self.setGraphicsEffect(shadow)

    def enterEvent(self, event):
        """Enhanced hover effect with smooth transitions"""
        self.is_hovered = True
        # Enhanced shadow on hover
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)

        # Update cursor to indicate interactivity
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Return to normal state"""
        self.is_hovered = False
        # Return to normal shadow
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(6)
        shadow.setColor(QColor(0, 0, 0, 40))
        self.setGraphicsEffect(shadow)
        super().leaveEvent(event)

    def mouseReleaseEvent(self, event):
        child = self.childAt(event.position().toPoint())
        if isinstance(child, ClickableLabel):
            return
        self.open_file_requested.emit(self.file_data)
        
class FileListCard(QFrame):
    """Modern responsive file card widget optimized for list view with gradient design."""
    open_file_requested = Signal(dict)
    open_folder_requested = Signal(dict)

    def __init__(self, file_data, gradient_colors, parent=None):
        super().__init__(parent)
        self.file_data = file_data
        self.gradient_colors = gradient_colors
        self.is_hovered = False
        
        # Set responsive height based on content
        self.setMinimumHeight(80)
        self.setMaximumHeight(120)
        self.setMinimumWidth(400)  # Reduced minimum width for better responsiveness
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.init_ui()
        self.apply_styles()

    def init_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 16, 20, 16)  # Increased vertical margins
        main_layout.setSpacing(20)  # Increased spacing between sections

        # Left section: Icon + File name
        left_section = QHBoxLayout()
        left_section.setSpacing(15)  # Increased spacing
        
        # Excel icon
        icon_label = QLabel("📊")
        icon_label.setStyleSheet("""
            font-size: 28px; 
            color: white; 
            font-weight: bold;
            background: rgba(0, 0, 0, 0.25);
            border-radius: 8px;
            padding: 8px 10px;
        """)
        icon_label.setFixedSize(48, 48)  # Larger icon
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_section.addWidget(icon_label)

        # File name
        name_label = QLabel(self.file_data["name"])
        name_label.setFont(QFont("Segoe UI", 13, QFont.Weight.Bold))  # Larger font
        name_label.setStyleSheet("""
            color: white; 
            font-weight: 600;
            background: transparent;
        """)
        name_label.setWordWrap(True)
        name_label.setToolTip(self.file_data["name"])
        left_section.addWidget(name_label, 1)
        
        main_layout.addLayout(left_section, 2)

        # Middle section: Path
        path_section = QVBoxLayout()
        path_section.setSpacing(6)  # Increased spacing
        
        # Path label
        path_header = QLabel("📁 Location:")
        path_header.setStyleSheet("""
            color: rgba(255, 255, 255, 0.7); 
            font-size: 10pt;
            font-weight: 500;
        """)
        path_section.addWidget(path_header)
        
        path_label = ClickableLabel(os.path.dirname(self.file_data["path"]))
        path_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9); 
            font-size: 11pt;
            background: transparent;
            padding: 2px 0px;
        """)
        path_label.setToolTip(f"Click to open: {self.file_data['path']}")
        path_label.clicked.connect(lambda: self.open_folder_requested.emit(self.file_data))
        path_label.setWordWrap(True)  # Allow wrapping for long paths
        path_section.addWidget(path_label)
        
        main_layout.addLayout(path_section, 2)

        # Right section: File info
        info_section = QVBoxLayout()
        info_section.setSpacing(8)  # Increased spacing
        info_section.setAlignment(Qt.AlignmentFlag.AlignCenter)  # Center align for better look
        
        # File size
        size_info = QLabel(f"📊 {self._get_file_size()}")
        size_info.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 10pt;
            font-weight: 500;
        """)
        info_section.addWidget(size_info)
        
        # Modified date
        date_info = QLabel(f"🕒 {self._get_file_date()}")
        date_info.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 10pt;
            font-weight: 500;
        """)
        info_section.addWidget(date_info)
        
        main_layout.addLayout(info_section, 1)

    def _get_file_size(self):
        """Get human readable file size."""
        try:
            size_bytes = os.path.getsize(self.file_data["path"])
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024**2:
                return f"{size_bytes/1024:.1f} KB"
            elif size_bytes < 1024**3:
                return f"{size_bytes/(1024**2):.1f} MB"
            else:
                return f"{size_bytes/(1024**3):.1f} GB"
        except:
            return "Unknown"

    def _get_file_date(self):
        """Get human readable file modification date."""
        try:
            timestamp = os.path.getmtime(self.file_data["path"])
            return datetime.fromtimestamp(timestamp).strftime("%m/%d/%Y %H:%M")
        except:
            return "Unknown"

    def apply_styles(self):
        # Modern gradient background similar to FileCard
        gradient_style = f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.gradient_colors[0]}, 
                    stop:1 {self.gradient_colors[1]});
                border-radius: 12px;
                border: none;
                margin: 4px 0px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.gradient_colors[0]}, 
                    stop:0.3 {self.gradient_colors[1]},
                    stop:1 {self.gradient_colors[0]});
            }}
        """
        self.setStyleSheet(gradient_style)
        
        # Modern shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 40))
        self.setGraphicsEffect(shadow)

    def enterEvent(self, event):
        """Hover effect"""
        self.is_hovered = True
        # Increase shadow on hover
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setXOffset(0)
        shadow.setYOffset(8)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Leave hover effect"""
        self.is_hovered = False
        # Return to normal shadow
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 40))
        self.setGraphicsEffect(shadow)
        super().leaveEvent(event)

    def mouseReleaseEvent(self, event):
        child = self.childAt(event.position().toPoint())
        if isinstance(child, ClickableLabel):
            return
        self.open_file_requested.emit(self.file_data)

class GridFolderSeparator(QFrame):
    """Simple horizontal line separator for grid view."""
    open_folder_requested = Signal(str)

    def __init__(self, folder_path, file_count=0, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.file_count = file_count

        self.setFixedHeight(1)
        self.init_ui()

    def init_ui(self):
        # Just a simple horizontal line
        self.setFrameShape(QFrame.Shape.HLine)
        self.setStyleSheet("""
            QFrame {
                background-color: #e0e0e0;
                border: none;
                margin: 8px 0px;
            }
        """)

class FolderSeparator(QFrame):
    """Simple horizontal line separator for folders."""
    open_folder_requested = Signal(str)

    def __init__(self, folder_path, file_count=0, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.file_count = file_count

        self.setFixedHeight(1)
        self.init_ui()

    def init_ui(self):
        # Just a simple horizontal line
        self.setFrameShape(QFrame.Shape.HLine)
        self.setStyleSheet("""
            QFrame {
                background-color: #e0e0e0;
                border: none;
                margin: 8px 0px;
            }
        """)

class FileListWidget(QScrollArea):
    """List view widget with gradient cards similar to grid view."""
    open_file_requested = Signal(dict)
    open_folder_requested = Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Create scroll content widget
        self.scroll_content = QWidget()
        self.content_layout = QVBoxLayout(self.scroll_content)
        self.content_layout.setContentsMargins(15, 15, 15, 15)  # Increased margins
        self.content_layout.setSpacing(8)  # Reduced spacing for tighter grouping
        self.content_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        self.setWidget(self.scroll_content)
        
        self.setStyleSheet("""
            QScrollArea {
                background-color: #f5f5f7;
                border: none;
                border-radius: 8px;
            }
            QScrollBar:vertical {
                border: none;
                background: #f5f5f7;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 20px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
        """)

    def populate_files(self, files_data):
        """Populate the list with gradient card widgets grouped by folder."""
        self.clear()
        
        if not files_data:
            return
        
        # Group files by folder
        folder_groups = {}
        for file_data in files_data:
            folder_path = os.path.dirname(file_data["path"])
            if folder_path not in folder_groups:
                folder_groups[folder_path] = []
            folder_groups[folder_path].append(file_data)
        
        # Sort folders alphabetically
        sorted_folders = sorted(folder_groups.keys())
        
        card_index = 0
        for folder_path in sorted_folders:
            folder_files = folder_groups[folder_path]
            
            # Add folder separator
            separator = FolderSeparator(folder_path, len(folder_files))
            separator.open_folder_requested.connect(lambda path, fp=folder_path: self.open_folder_requested.emit({"path": fp}))
            self.content_layout.addWidget(separator)

            # Add files in this folder
            for file_data in folder_files:
                gradient_colors = COLOR_GRADIENTS[card_index % len(COLOR_GRADIENTS)]
                card = FileListCard(file_data, gradient_colors)
                card.open_file_requested.connect(lambda d, fd=file_data: self.open_file_requested.emit(fd))
                card.open_folder_requested.connect(lambda d, fd=file_data: self.open_folder_requested.emit(fd))

                self.content_layout.addWidget(card)
                card_index += 1
            
            # Natural spacing between folder groups handled by margin

    def clear(self):
        """Clear all cards from the layout."""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

class ExcelManagerApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.all_files_data = []; self.file_path_map = {}
        self.num_columns = 3 # Support up to 3 columns for smaller cards
        self.current_view_mode = "grid"  # "grid" or "list"
        self.current_sort_by = "name"    # "name", "date", "size", "path"
        self.current_sort_order = "asc"  # "asc" or "desc"
        
        # Performance and threading
        self.scan_worker = None
        self.progress_dialog = None
        self.files_being_added = []  # Batch processing
        
        # Responsive design properties
        self.current_card_width = CARD_MIN_WIDTH
        self.current_margin = MARGIN_MEDIUM
        self.current_screen_size = "medium"
        
        # Set minimum window size for better responsiveness
        self.setMinimumSize(MIN_WIDTH, MIN_HEIGHT)
        
        self.init_ui()
        self._setup_keyboard_shortcuts()  # Add keyboard shortcuts
        self._load_app_state()  # Load state from previous session

    def init_ui(self):
        self.setWindowTitle(APP_NAME); self.setGeometry(100, 100, WIDTH, HEIGHT)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(self.current_margin, self.current_margin, self.current_margin, self.current_margin)
        main_layout.setSpacing(self.current_margin)
        main_layout.addWidget(self._create_top_bar())
        
        # Create both view containers
        self.scroll_area = QScrollArea(); self.scroll_area.setWidgetResizable(True)
        self.scroll_content_widget = QWidget()
        self.card_layout = QGridLayout(self.scroll_content_widget)
        self.card_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
        self.card_layout.setSpacing(GRID_SPACING)
        self.scroll_area.setWidget(self.scroll_content_widget)
        
        # Create list view
        self.list_widget = FileListWidget()
        self.list_widget.open_file_requested.connect(lambda d: self._handle_open_file(d['path']))
        self.list_widget.open_folder_requested.connect(lambda d: self._handle_open_folder(d['path']))
        
        # Add both to main layout (we'll show/hide based on view mode)
        main_layout.addWidget(self.scroll_area)
        main_layout.addWidget(self.list_widget)
        
        self.status_bar = QStatusBar(); self.setStatusBar(self.status_bar)
        self.status_label = QLabel("Ready"); self.status_bar.addWidget(self.status_label)
        
        # Set initial view mode
        self._update_view_mode()
        
        # Perform initial responsive layout calculation
        self._update_responsive_layout()
        
        if not self.all_files_data:
            self._show_welcome_message()

    def resizeEvent(self, event):
        """Enhanced responsive recalculation when window is resized."""
        super().resizeEvent(event)
        self._update_responsive_layout()
        
    def _update_responsive_layout(self):
        """Update layout based on current window size using responsive breakpoints."""
        window_width = self.width()
        
        # Determine screen size category
        if window_width <= BREAKPOINT_SMALL:
            new_screen_size = "small"
            new_margin = MARGIN_SMALL
            new_card_width = max(CARD_MIN_WIDTH, min(window_width - 80, CARD_MAX_WIDTH))
        elif window_width <= BREAKPOINT_MEDIUM:
            new_screen_size = "medium"
            new_margin = MARGIN_MEDIUM
            new_card_width = max(CARD_MIN_WIDTH, min((window_width - 100) // 2 - GRID_SPACING, CARD_MAX_WIDTH))
        else:
            new_screen_size = "large"
            new_margin = MARGIN_LARGE
            new_card_width = max(CARD_MIN_WIDTH, min((window_width - 120) // 3 - GRID_SPACING, CARD_MAX_WIDTH))
        
        # Calculate optimal number of columns based on available space
        if hasattr(self, 'scroll_area'):
            scroll_area_width = self.scroll_area.width() - (2 * new_margin)
            available_width = scroll_area_width - (2 * GRID_SPACING)
            
            # Calculate how many cards can fit
            card_with_spacing = new_card_width + GRID_SPACING
            new_num_columns = max(1, available_width // card_with_spacing)
            
            # Limit columns based on screen size for better UX
            if new_screen_size == "small":
                new_num_columns = min(new_num_columns, 1)
            elif new_screen_size == "medium":
                new_num_columns = min(new_num_columns, 2)
            else:
                new_num_columns = min(new_num_columns, 4)  # Max 4 columns even on large screens
        else:
            new_num_columns = 1
            
        # Update layout if anything changed
        layout_changed = (new_num_columns != self.num_columns or 
                         new_card_width != self.current_card_width or
                         new_screen_size != self.current_screen_size)
                         
        if layout_changed:
            self.num_columns = new_num_columns
            self.current_card_width = new_card_width
            self.current_margin = new_margin
            self.current_screen_size = new_screen_size
            
            # Update margins
            if hasattr(self, 'centralWidget'):
                main_layout = self.centralWidget().layout()
                if main_layout:
                    main_layout.setContentsMargins(new_margin, new_margin, new_margin, new_margin)
            
            # Update responsive styles for UI elements
            self._update_responsive_styles()
            
            # Refresh display to apply new sizing
            self._filter_files()
    
    def _update_responsive_styles(self):
        """Update all responsive UI element styles when screen size changes."""
        # Update button styles
        if hasattr(self, 'view_toggle_btn'):
            self._apply_responsive_button_style(self.view_toggle_btn)
        
        # Update combo box styles
        if hasattr(self, 'sort_combo'):
            self._apply_responsive_combo_style(self.sort_combo)
            
        # Update search box styles
        if hasattr(self, 'search_entry'):
            self._apply_responsive_search_style(self.search_entry)
            
        # Update sort order button
        if hasattr(self, 'sort_order_btn'):
            self._apply_responsive_button_style(self.sort_order_btn, compact=True)
            
        # Find and update all action buttons
        if hasattr(self, 'first_row_layout'):
            for i in range(self.first_row_layout.count()):
                item = self.first_row_layout.itemAt(i)
                if item and item.widget() and isinstance(item.widget(), QPushButton):
                    widget = item.widget()
                    if widget.text() == "🗑️ Clear All":
                        self._apply_responsive_button_style(widget, is_danger=True)
                    elif widget != self.view_toggle_btn:  # Don't double-style view toggle
                        self._apply_responsive_button_style(widget)
        
        # Update button texts for responsive display
        self._update_responsive_button_texts()
    
    def _update_responsive_button_texts(self):
        """Update button texts based on current screen size."""
        # Update view toggle button text
        if hasattr(self, 'view_toggle_btn'):
            if self.current_view_mode == "grid":
                text = "📋 List" if self.current_screen_size == "small" else "📋 List View"
            else:
                text = "🔲 Grid" if self.current_screen_size == "small" else "🔲 Grid View"
            self.view_toggle_btn.setText(text)
        
        # Update sort order button text
        if hasattr(self, 'sort_order_btn'):
            if self.current_sort_order == "asc":
                text = "↑ Asc" if self.current_screen_size == "small" else "↑ Ascending"
            else:
                text = "↓ Desc" if self.current_screen_size == "small" else "↓ Descending"
            self.sort_order_btn.setText(text)

    def _update_display(self, files_to_display):
        # Sort files based on current sort settings
        sorted_files = self._sort_files(files_to_display)
        
        if self.current_view_mode == "grid":
            self._update_grid_display(sorted_files)
        else:
            self._update_list_display(sorted_files)
        
        self._update_status_bar(len(sorted_files))

    def _update_grid_display(self, files_to_display):
        """Update responsive grid view display with folder grouping."""
        self._clear_layout(self.card_layout)
        if not files_to_display:
            self._show_welcome_message()
            return

        # Group files by folder
        folder_groups = {}
        for file_data in files_to_display:
            folder_path = os.path.dirname(file_data["path"])
            if folder_path not in folder_groups:
                folder_groups[folder_path] = []
            folder_groups[folder_path].append(file_data)
        
        # Sort folders alphabetically
        sorted_folders = sorted(folder_groups.keys())
        
        row, col = 0, 0
        card_index = 0
        
        for folder_path in sorted_folders:
            folder_files = folder_groups[folder_path]
            
            # Add folder separator spanning full width
            if col > 0:  # If we're not at the start of a row, move to next row
                row += 1
                col = 0
            
            separator = GridFolderSeparator(folder_path, len(folder_files))
            separator.open_folder_requested.connect(lambda path, fp=folder_path: self._handle_open_folder(fp))
            self.card_layout.addWidget(separator, row, 0, 1, self.num_columns)  # Span all columns
            row += 1
            col = 0
            
            # Add files in this folder
            for file_data in folder_files:
                gradient_colors = COLOR_GRADIENTS[card_index % len(COLOR_GRADIENTS)]
                card = FileCard(file_data, gradient_colors, self.current_card_width)
                card.open_file_requested.connect(lambda d, fd=file_data: self._handle_open_file(fd['path']))
                card.open_folder_requested.connect(lambda d, fd=file_data: self._handle_open_folder(fd['path']))

                self.card_layout.addWidget(card, row, col)
                col += 1
                if col >= self.num_columns:
                    col = 0
                    row += 1
                card_index += 1
            
            # Natural spacing between folder groups handled by separator margin

    def _update_list_display(self, files_to_display):
        """Update list view display."""
        if not files_to_display:
            self.list_widget.clear()
            return
        
        self.list_widget.populate_files(files_to_display)

    def _sort_files(self, files_data):
        """Sort files based on current sort settings."""
        if not files_data:
            return files_data
            
        reverse_order = self.current_sort_order == "desc"
        
        if self.current_sort_by == "name":
            return sorted(files_data, key=lambda x: x["name"].lower(), reverse=reverse_order)
        elif self.current_sort_by == "path":
            return sorted(files_data, key=lambda x: x["path"].lower(), reverse=reverse_order)
        elif self.current_sort_by == "date":
            return sorted(files_data, key=lambda x: self._get_file_mtime(x["path"]), reverse=reverse_order)
        elif self.current_sort_by == "size":
            return sorted(files_data, key=lambda x: self._get_file_size_bytes(x["path"]), reverse=reverse_order)
        
        return files_data

    def _get_file_mtime(self, file_path):
        """Get file modification time for sorting."""
        try:
            return os.path.getmtime(file_path)
        except:
            return 0

    def _get_file_size_bytes(self, file_path):
        """Get file size in bytes for sorting."""
        try:
            return os.path.getsize(file_path)
        except:
            return 0

    def _update_view_mode(self):
        """Update visibility based on current view mode."""
        if self.current_view_mode == "grid":
            self.scroll_area.setVisible(True)
            self.list_widget.setVisible(False)
        else:
            self.scroll_area.setVisible(False)
            self.list_widget.setVisible(True)

    def _create_top_bar(self):
        """Create responsive top bar that adapts to screen size."""
        # Container widget for the entire top bar
        top_container = QWidget()
        container_layout = QVBoxLayout(top_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(8)
        
        # First row: Action buttons
        first_row = QHBoxLayout()
        first_row.setSpacing(8)
        
        # Buttons with responsive sizing
        btn_scan_folder = QPushButton("📁 Scan Folder")
        btn_add_files = QPushButton("➕ Add Files")  
        btn_clear = QPushButton("🗑️ Clear All")
        
        # Responsive button style - adapts padding based on screen size
        self._apply_responsive_button_style(btn_scan_folder)
        self._apply_responsive_button_style(btn_add_files)
        self._apply_responsive_button_style(btn_clear, is_danger=True)
        
        btn_scan_folder.clicked.connect(self._scan_folder)
        btn_add_files.clicked.connect(self._add_individual_files)
        btn_clear.clicked.connect(self._clear_all_files)
        
        first_row.addWidget(btn_scan_folder)
        first_row.addWidget(btn_add_files)
        first_row.addWidget(btn_clear)
        
        # Add flexible space
        first_row.addStretch(1)
        
        # View mode toggle
        self.view_toggle_btn = QPushButton("📋 List View")
        self._apply_responsive_button_style(self.view_toggle_btn)
        self.view_toggle_btn.clicked.connect(self._toggle_view_mode)
        first_row.addWidget(self.view_toggle_btn)
        
        container_layout.addLayout(first_row)
        
        # Second row: Sort options and search (responsive)
        second_row = QHBoxLayout()
        second_row.setSpacing(8)
        
        # Sort options container
        sort_container = QHBoxLayout()
        sort_container.setSpacing(8)
        
        sort_label = QLabel("Sort by:")
        sort_label.setStyleSheet("color: #495057; font-weight: 600; font-size: 10pt;")
        sort_container.addWidget(sort_label)
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Name", "Date Modified", "File Size", "Path"])
        self._apply_responsive_combo_style(self.sort_combo)
        self.sort_combo.currentTextChanged.connect(self._on_sort_changed)
        sort_container.addWidget(self.sort_combo)
        
        # Sort order toggle
        self.sort_order_btn = QPushButton("↑ Asc")
        self._apply_responsive_button_style(self.sort_order_btn, compact=True)
        self.sort_order_btn.clicked.connect(self._toggle_sort_order)
        sort_container.addWidget(self.sort_order_btn)
        
        second_row.addLayout(sort_container)
        second_row.addStretch(1)
        
        # Search box with responsive width
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("🔍 Search files...")
        self._apply_responsive_search_style(self.search_entry)
        self.search_entry.textChanged.connect(self._filter_files)
        second_row.addWidget(self.search_entry)
        
        container_layout.addLayout(second_row)
        
        # Store references for responsive updates
        self.top_bar_container = top_container
        self.first_row_layout = first_row
        self.second_row_layout = second_row
        
        return top_container

    def _setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for improved accessibility and productivity."""
        # Scan folder shortcut
        scan_shortcut = QShortcut(QKeySequence("Ctrl+O"), self)
        scan_shortcut.activated.connect(self._scan_folder)

        # Add files shortcut
        add_files_shortcut = QShortcut(QKeySequence("Ctrl+Shift+O"), self)
        add_files_shortcut.activated.connect(self._add_individual_files)

        # Clear all shortcut
        clear_shortcut = QShortcut(QKeySequence("Ctrl+Shift+C"), self)
        clear_shortcut.activated.connect(self._clear_all_files)

        # Toggle view mode
        view_toggle_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        view_toggle_shortcut.activated.connect(self._toggle_view_mode)

        # Focus search box
        search_focus_shortcut = QShortcut(QKeySequence("Ctrl+F"), self)
        search_focus_shortcut.activated.connect(lambda: self.search_entry.setFocus())

        # Refresh/reload
        refresh_shortcut = QShortcut(QKeySequence("F5"), self)
        refresh_shortcut.activated.connect(self._filter_files)

        # Escape to clear search
        escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        escape_shortcut.activated.connect(self._clear_search)

        # Add tooltips with shortcuts to buttons
        if hasattr(self, 'first_row_layout'):
            # We'll update button tooltips after they're created
            pass

    def _clear_search(self):
        """Clear search box and show all files."""
        self.search_entry.clear()
        self._filter_files()

    def _apply_responsive_button_style(self, button, is_danger=False, compact=False):
        """Apply responsive button styling based on current screen size."""
        base_padding = "8px 12px" if compact else "10px 16px"
        font_size = "10pt" if compact else "11pt"
        
        if self.current_screen_size == "small":
            padding = "6px 10px" if compact else "8px 12px"
            font_size = "9pt" if compact else "10pt"
        elif self.current_screen_size == "medium":
            padding = "8px 12px" if compact else "10px 16px"
            font_size = "10pt" if compact else "11pt"
        else:
            padding = "10px 16px" if compact else "12px 20px"
            font_size = "11pt" if compact else "12pt"
        
        if is_danger:
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f8d7da, stop:1 #f5c6cb);
                    border: 1px solid #f5c6cb;
                    padding: {padding};
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: {font_size};
                    color: #721c24;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f5c6cb, stop:1 #f1b0b7);
                    border: 1px solid #f1b0b7;
                }}
            """
        else:
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f8f9fa, stop:1 #e9ecef);
                    border: 1px solid #dee2e6;
                    padding: {padding};
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: {font_size};
                    color: #495057;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e9ecef, stop:1 #dee2e6);
                    border: 1px solid #adb5bd;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #dee2e6, stop:1 #ced4da);
                }}
            """
        button.setStyleSheet(style)
    
    def _apply_responsive_combo_style(self, combo):
        """Apply responsive combo box styling."""
        min_width = "100px" if self.current_screen_size == "small" else "120px"
        font_size = "9pt" if self.current_screen_size == "small" else "10pt"
        
        style = f"""
            QComboBox {{
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                padding: 6px 10px;
                border-radius: 6px;
                font-size: {font_size};
                min-width: {min_width};
            }}
            QComboBox:focus {{
                border: 2px solid #667eea;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                width: 10px;
                height: 10px;
            }}
        """
        combo.setStyleSheet(style)
    
    def _apply_responsive_search_style(self, search_entry):
        """Apply responsive search box styling."""
        if self.current_screen_size == "small":
            min_width = 180
            padding = "8px 12px"
            font_size = "9pt"
        elif self.current_screen_size == "medium":
            min_width = 240
            padding = "10px 14px"
            font_size = "10pt"
        else:
            min_width = 300
            padding = "12px 16px"
            font_size = "11pt"
        
        style = f"""
            QLineEdit {{
                background-color: #ffffff;
                border: 2px solid #e9ecef;
                padding: {padding};
                border-radius: 8px;
                font-size: {font_size};
                color: #495057;
                min-width: {min_width}px;
            }}
            QLineEdit:focus {{
                border: 2px solid #667eea;
                background-color: #f8f9ff;
            }}
            QLineEdit::placeholder {{
                color: #adb5bd;
                font-style: italic;
            }}
        """
        search_entry.setStyleSheet(style)
        search_entry.setMinimumWidth(min_width)

    def _show_welcome_message(self):
        self._clear_layout(self.card_layout)
        welcome_widget = QWidget()
        layout = QVBoxLayout(welcome_widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(20)

        # Icon and title with modern design
        icon_title_layout = QHBoxLayout()
        icon_title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_title_layout.setSpacing(15)
        
        # Large icon with gradient
        main_icon = QLabel("📊")
        main_icon.setStyleSheet("""
            font-size: 48px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 25px;
            padding: 15px;
            color: white;
        """)
        main_icon.setFixedSize(80, 80)
        main_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_title_layout.addWidget(main_icon)
        
        # Title with gradient text effect
        title = QLabel("Excel Grid Hub")
        title.setStyleSheet("""
            font-size: 32pt; 
            font-weight: 700; 
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            text-align: center;
            font-family: 'Segoe UI', sans-serif;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_title_layout.addWidget(title)
        
        layout.addLayout(icon_title_layout)
        
        # Subtitle
        subtitle = QLabel("Manage and quickly access your Excel files")
        subtitle.setStyleSheet("""
            font-size: 14pt; 
            color: #666; 
            text-align: center;
            margin-top: 10px;
            font-weight: 400;
        """)
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle, alignment=Qt.AlignmentFlag.AlignCenter)
        
        self.card_layout.addWidget(welcome_widget, 0, 0, 1, self.num_columns, Qt.AlignmentFlag.AlignCenter)
        self._update_status_bar()
            
    def _scan_folder(self):
        """Scan folder for Excel files using background thread."""
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder to Scan")
        if not folder_path:
            return
        
        # Check if scan is already running
        if self.scan_worker and self.scan_worker.isRunning():
            QMessageBox.information(self, "Scan in Progress", "A scan is already running. Please wait for it to complete.")
            return
        
        # Create progress dialog
        self.progress_dialog = QProgressDialog("Preparing to scan...", "Cancel", 0, 100, self)
        self.progress_dialog.setWindowTitle("Scanning for Excel Files")
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.setMinimumDuration(0)
        self.progress_dialog.setStyleSheet("""
            QProgressDialog {
                background-color: #f5f5f7;
                border-radius: 8px;
            }
            QProgressBar {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 6px;
            }
        """)
        
        # Initialize batch processing
        self.files_being_added = []
        
        # Create and start worker thread
        self.scan_worker = FileScanWorker(folder_path, self)
        self.scan_worker.file_found.connect(self._on_file_found)
        self.scan_worker.progress_update.connect(self._on_scan_progress)
        self.scan_worker.finished.connect(self._on_scan_finished)
        self.scan_worker.error.connect(self._on_scan_error)
        
        # Connect cancel button
        self.progress_dialog.canceled.connect(self._cancel_scan)
        
        # Start scanning
        self.scan_worker.start()
        self.progress_dialog.show()

    def _on_file_found(self, file_path):
        """Handle when a file is found during scanning."""
        self.files_being_added.append(file_path)
        
        # Batch process every 10 files for better performance
        if len(self.files_being_added) >= 10:
            self._process_file_batch()

    def _on_scan_progress(self, progress, current_file):
        """Update progress dialog."""
        if self.progress_dialog and not self.progress_dialog.wasCanceled():
            self.progress_dialog.setValue(progress)
            self.progress_dialog.setLabelText(f"Scanning: {current_file}")

    def _on_scan_finished(self, files_found):
        """Handle scan completion."""
        # Process remaining files
        if self.files_being_added:
            self._process_file_batch()
        
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        # Update display
        self._filter_files()
        self._save_app_state()
        
        # Show completion message
        QMessageBox.information(self, "Scan Complete", 
                               f"Found and added {files_found} Excel files.")

    def _on_scan_error(self, error_message):
        """Handle scan error."""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        QMessageBox.critical(self, "Scan Error", f"Error during scanning:\n{error_message}")

    def _cancel_scan(self):
        """Cancel the current scan operation."""
        if self.scan_worker:
            self.scan_worker.stop()
            self.scan_worker.wait()  # Wait for thread to finish
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

    def _process_file_batch(self):
        """Process a batch of files for better performance."""
        added_count = 0
        for file_path in self.files_being_added:
            if self._add_file_to_list(file_path):
                added_count += 1
        
        self.files_being_added.clear()
        
        # Update status
        if added_count > 0:
            current_count = len(self.all_files_data)
            self.status_label.setText(f"Added {added_count} files. Total: {current_count} files.")
            
            # Update display every batch for responsive UI
            self._filter_files()

    def _add_individual_files(self):
        """Add individual Excel files with improved detection and performance."""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, 
            "Select Excel Files", 
            "", 
            "Excel Files (*.xlsx *.xls *.xlsm *.xlsb *.xltx *.xltm *.xlam *.xla *.xlw *.csv);;All Files (*)"
        )
        if not file_paths:
            return
        
        # Show progress for large number of files
        if len(file_paths) > 20:
            progress = QProgressDialog("Adding files...", "Cancel", 0, len(file_paths), self)
            progress.setWindowTitle("Adding Excel Files")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(0)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f5f5f7;
                    border-radius: 8px;
                }
                QProgressBar {
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    text-align: center;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #667eea, stop:1 #764ba2);
                    border-radius: 6px;
                }
            """)
        else:
            progress = None
        
        added_count = 0
        invalid_files = []
        
        for i, path in enumerate(file_paths):
            if progress:
                progress.setValue(i)
                progress.setLabelText(f"Processing: {os.path.basename(path)}")
                QApplication.processEvents()  # Keep UI responsive
                
                if progress.wasCanceled():
                    break
            
            # Validate Excel file
            if is_excel_file(path):
                if self._add_file_to_list(path):
                    added_count += 1
            else:
                invalid_files.append(os.path.basename(path))
        
        if progress:
            progress.close()
        
        # Show results
        message = f"Added {added_count} new files."
        if invalid_files:
            if len(invalid_files) <= 5:
                message += f"\n\nSkipped invalid files: {', '.join(invalid_files)}"
            else:
                message += f"\n\nSkipped {len(invalid_files)} invalid files."
        
        if len(file_paths) - added_count > len(invalid_files):
            duplicates = len(file_paths) - added_count - len(invalid_files)
            if duplicates > 0:
                message += f"\n{duplicates} files were already in the list."
        
        if added_count > 0 or invalid_files:
            QMessageBox.information(self, "Add Files Complete", message)
        
        if added_count > 0:
            self._filter_files()
            self._save_app_state()

    def _filter_files(self):
        """Enhanced filter files with fuzzy search and multiple criteria."""
        search_text = self.search_entry.text().strip()

        if not search_text:
            # Show all files if no search text
            self._update_display(self.all_files_data)
            return

        # Parse search query for advanced filtering
        search_terms = search_text.lower().split()
        filtered_files = []

        for file_data in self.all_files_data:
            file_name = file_data["name"].lower()
            file_path = file_data["path"].lower()
            folder_name = os.path.dirname(file_data["path"]).lower()

            # Calculate relevance score
            score = 0

            # Exact matches get highest score
            if search_text.lower() in file_name:
                score += 100

            # Partial matches in filename
            for term in search_terms:
                if term in file_name:
                    score += 50
                elif term in folder_name:
                    score += 25
                elif term in file_path:
                    score += 10

            # Fuzzy matching for typos (simple implementation)
            if score == 0:
                for term in search_terms:
                    if len(term) > 3:  # Only for longer terms
                        # Check if term is similar to parts of filename
                        name_parts = file_name.replace('.', ' ').replace('_', ' ').replace('-', ' ').split()
                        for part in name_parts:
                            if len(part) > 2 and self._fuzzy_match(term, part):
                                score += 15

            if score > 0:
                file_data_with_score = file_data.copy()
                file_data_with_score['_search_score'] = score
                filtered_files.append(file_data_with_score)

        # Sort by relevance score (highest first)
        filtered_files.sort(key=lambda x: x.get('_search_score', 0), reverse=True)

        self._update_display(filtered_files)

    def _fuzzy_match(self, term, target, threshold=0.7):
        """Simple fuzzy matching for search terms."""
        if len(term) == 0 or len(target) == 0:
            return False

        # Simple character overlap ratio
        common_chars = set(term) & set(target)
        ratio = len(common_chars) / max(len(set(term)), len(set(target)))

        return ratio >= threshold

    def _handle_open_file(self, path): 
        QDesktopServices.openUrl(QUrl.fromLocalFile(path))
        
    def _handle_open_folder(self, path): QDesktopServices.openUrl(QUrl.fromLocalFile(os.path.dirname(path)))

    def _toggle_view_mode(self):
        """Toggle between grid and list view modes."""
        if self.current_view_mode == "grid":
            self.current_view_mode = "list"
            # Use shorter text for smaller screens
            text = "🔲 Grid" if self.current_screen_size == "small" else "🔲 Grid View"
            self.view_toggle_btn.setText(text)
        else:
            self.current_view_mode = "grid"
            # Use shorter text for smaller screens
            text = "📋 List" if self.current_screen_size == "small" else "📋 List View"
            self.view_toggle_btn.setText(text)
        
        self._update_view_mode()
        self._filter_files()  # Refresh display with current view

    def _on_sort_changed(self, sort_text):
        """Handle sort option change."""
        sort_mapping = {
            "Name": "name",
            "Date Modified": "date", 
            "File Size": "size",
            "Path": "path"
        }
        self.current_sort_by = sort_mapping.get(sort_text, "name")
        self._filter_files()  # Refresh display with new sort

    def _toggle_sort_order(self):
        """Toggle between ascending and descending sort order."""
        if self.current_sort_order == "asc":
            self.current_sort_order = "desc"
            # Use shorter text for smaller screens
            text = "↓ Desc" if self.current_screen_size == "small" else "↓ Descending"
            self.sort_order_btn.setText(text)
        else:
            self.current_sort_order = "asc"
            # Use shorter text for smaller screens
            text = "↑ Asc" if self.current_screen_size == "small" else "↑ Ascending"
            self.sort_order_btn.setText(text)
        
        self._filter_files()  # Refresh display with new sort order

    def _clear_all_files(self):
        if not self.all_files_data: return
        reply = QMessageBox.question(self, "Confirm", "Remove all files from the list?", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.all_files_data.clear(); self.file_path_map.clear(); self.search_entry.clear(); self._filter_files()
            self._save_app_state()  # Save state after clearing

    def _add_file_to_list(self, file_path):
        """Add file to list with improved validation and performance."""
        # Normalize path for consistent comparison
        try:
            normalized_path = os.path.normpath(os.path.abspath(file_path))
        except:
            return False
        
        # Check if already exists
        if normalized_path in self.file_path_map:
            return False
        
        # Double-check if it's a valid Excel file
        if not is_excel_file(normalized_path):
            return False
        
        # Create file data
        file_name = os.path.basename(normalized_path)
        file_data = {"name": file_name, "path": normalized_path}
        
        # Add to collections
        self.all_files_data.append(file_data)
        self.file_path_map[normalized_path] = file_data
        
        return True
    
    def _clear_layout(self, layout):
        while layout.count():
            child = layout.takeAt(0)
            if child.widget(): child.widget().deleteLater()
    
    def _update_status_bar(self, displayed_count=0):
        total_count = len(self.all_files_data)
        if total_count == 0: self.status_label.setText("Ready")
        else: self.status_label.setText(f"Showing {displayed_count} of {total_count} files.")

    def _load_app_state(self):
        """Load application state from previous session"""
        try:
            if os.path.exists(STATE_FILE):
                with open(STATE_FILE, 'r', encoding='utf-8') as f: 
                    data = json.load(f)
                    file_paths = data.get('file_paths', [])
                    
                    # Load view and sort preferences
                    self.current_view_mode = data.get('view_mode', 'grid')
                    self.current_sort_by = data.get('sort_by', 'name')
                    self.current_sort_order = data.get('sort_order', 'asc')
                    
                    # Update UI controls
                    if self.current_view_mode == "list":
                        text = "🔲 Grid" if self.current_screen_size == "small" else "🔲 Grid View"
                        self.view_toggle_btn.setText(text)
                    
                    sort_mapping_reverse = {
                        "name": "Name",
                        "date": "Date Modified",
                        "size": "File Size", 
                        "path": "Path"
                    }
                    sort_text = sort_mapping_reverse.get(self.current_sort_by, "Name")
                    self.sort_combo.setCurrentText(sort_text)
                    
                    if self.current_sort_order == "desc":
                        text = "↓ Desc" if self.current_screen_size == "small" else "↓ Descending"
                        self.sort_order_btn.setText(text)
                    
                    # Only load files that still exist
                    for file_path in file_paths:
                        if os.path.exists(file_path):
                            self._add_file_to_list(file_path)
                    if self.all_files_data:
                        self._update_view_mode()
                        self._filter_files()
        except (FileNotFoundError, json.JSONDecodeError): 
            pass

    def _save_app_state(self):
        """Save current application state"""
        try:
            state_data = {
                'file_paths': [file_data['path'] for file_data in self.all_files_data],
                'view_mode': self.current_view_mode,
                'sort_by': self.current_sort_by,
                'sort_order': self.current_sort_order
            }
            with open(STATE_FILE, 'w', encoding='utf-8') as f: 
                json.dump(state_data, f, indent=4, ensure_ascii=False)
        except IOError as e: 
            QMessageBox.critical(self, "Error", f"Cannot save application state:\n{e}")

    def closeEvent(self, event):
        """Save state when closing application and cleanup resources."""
        # Stop any running scan
        if self.scan_worker and self.scan_worker.isRunning():
            self.scan_worker.stop()
            self.scan_worker.wait(5000)  # Wait up to 5 seconds
        
        # Close progress dialog if open
        if self.progress_dialog:
            self.progress_dialog.close()
        
        self._save_app_state()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyleSheet(STYLESHEET)
    window = ExcelManagerApp()
    window.show()
    sys.exit(app.exec())